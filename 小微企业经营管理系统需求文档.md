# 小微企业经营管理系统需求文档

## 1. 项目概述

### 1.1 项目背景
小微企业在快速发展过程中，往往面临管理粗放、信息孤岛、利润不透明等问题。传统的部门分工模式导致业务、生产、财务各自为政，缺乏统一的利润导向。本系统专为小微企业设计，旨在用最小的成本和复杂度，建立以订单利润为核心的精益管理体系。

### 1.2 核心理念
**告别部门本位，回归客户价值**。每一份订单都是一个独立的经营单元(Profit Center)，让小微企业的每个人都能清楚看到自己的工作如何影响订单利润。

### 1.3 项目目标
- **降本增效**：通过数字化手段降低管理成本，提升运营效率
- **利润透明**：实现订单利润的实时可视化，让老板心中有数
- **流程简化**：打通业务、生产、财务的数据流，减少重复录入
- **决策支持**：提供简单易懂的数据分析，支持快速决策
- **分阶段实施**：支持模块化部署，企业可根据需要逐步上线

### 1.4 小微企业适配性说明
- **人员规模**：适用于5-50人的小微企业
- **技术要求**：无需专业IT人员，普通员工经简单培训即可使用
- **成本控制**：采用云部署模式，降低硬件投入和维护成本
- **快速见效**：核心功能可在1个月内上线并产生价值

## 2. 系统架构

### 2.1 总体架构
**一个中心，三大支柱，模块化部署**
- **一个中心**：客户订单（所有数据、流程、权限都与订单ID强关联）
- **三大支柱**：业务前台、交付中台、财报后台
- **模块化设计**：支持分阶段实施，企业可根据需要选择部分模块先上线

### 2.2 技术架构规范

#### 2.2.1 前端技术栈
- **框架**：React 18+ (函数式组件 + Hooks)
- **UI组件库**：Ant Design (企业级UI设计语言)
- **状态管理**：Redux Toolkit (简化状态管理)
- **路由**：React Router v6
- **图表库**：ECharts (丰富的数据可视化)
- **移动端**：响应式设计，支持PWA

#### 2.2.2 后端技术栈
- **框架**：Spring Boot 3.x (简化配置，快速开发)
- **数据库**：PostgreSQL 15+ (开源、稳定、功能强大)
- **ORM**：Spring Data JPA + Hibernate
- **安全框架**：Spring Security + JWT
- **API文档**：Swagger/OpenAPI 3.0
- **缓存**：Redis (提升性能)

#### 2.2.3 部署架构
- **容器化**：Docker + Docker Compose (简化部署)
- **云部署**：支持阿里云、腾讯云等主流云平台
- **数据备份**：自动化数据备份策略
- **监控告警**：基础的系统监控和日志管理

#### 2.2.4 小微企业技术适配
- **低维护成本**：选择成熟稳定的技术栈，减少技术风险
- **简化部署**：提供一键部署脚本，无需专业运维人员
- **在线升级**：支持在线升级，减少停机时间
- **技术支持**：提供远程技术支持服务

## 3. 功能需求

### 3.1 业务前台 (Revenue Engine) - 优先级：高

#### 3.1.1 客户与商机管理 (CRM-Lite) - 【核心模块】
**功能描述**：轻量级客户关系管理，专为小微企业设计

**具体需求**：
- **客户基础信息**：公司名称、联系人、手机、微信、地址（必填项最少化）
- **客户分级**：简化为重要客户/普通客户两级（避免过度复杂）
- **销售机会**：商机来源、阶段（意向/报价/成交/流失）、预计金额
- **沟通记录**：支持语音转文字，降低录入成本
- **快速操作**：一键拨号、发送微信、查看历史订单

**小微企业适配优化**：
- **简化界面**：去除复杂的销售漏斗，采用简单的看板视图
- **移动优先**：支持手机端快速录入客户信息
- **导入便捷**：支持从Excel、微信通讯录快速导入

**验收标准**：
- 客户信息录入时间不超过2分钟
- 支持Excel批量导入（模板提供）
- 移动端操作流畅度达到原生APP水平

#### 3.1.2 智能报价与利润预估器 - 【核心模块】
**功能描述**：快速报价，实时利润预估，让小微企业告别拍脑袋定价

**具体需求**：
- **模板库管理**：预设常用产品/服务的标准报价模板
- **快速复制**：从历史订单一键复制BOM和价格
- **成本计算**：材料成本、人工成本、其他费用的简单加总
- **利润预估**：实时显示毛利率，红绿灯提示（绿色>30%，黄色15-30%，红色<15%）
- **报价单生成**：一键生成专业的PDF报价单

**小微企业适配优化**：
- **简化BOM**：支持简单的物料清单，无需复杂的多层级BOM
- **价格维护**：支持Excel导入物料价格，定期更新
- **快速报价**：常用产品5分钟内完成报价

**验收标准**：
- 常用产品报价时间不超过5分钟
- 报价单格式专业美观
- 毛利率计算准确率100%

#### 3.1.3 风险控制与审批 - 【可选模块】
**功能描述**：设定利润底线，防范低价竞争风险

**具体需求**：
- **利润红线**：设置最低毛利率（如15%），低于红线时强制提醒
- **简化审批**：只设置老板审批一级，避免流程复杂化
- **微信提醒**：低价订单自动发送微信提醒给老板

**小微企业适配优化**：
- **规则简单**：避免复杂的多维度规则设置
- **审批快速**：支持微信端快速审批
- **可关闭**：企业可选择关闭审批功能，完全信任业务员

**验收标准**：
- 预警响应时间不超过30秒
- 微信提醒到达率100%
- 审批操作可在1分钟内完成

#### 3.1.4 合同与订单转化 - 【核心模块】
**功能描述**：报价成功后快速转化为订单，开始利润跟踪

**具体需求**：
- **一键转化**：报价单一键转为正式订单
- **合同管理**：简单的合同条款模板（付款方式、交期等）
- **订单编号**：自动生成唯一订单编号
- **状态跟踪**：订单状态（待生产/生产中/已发货/已完成）

**小微企业适配优化**：
- **模板化**：提供标准合同模板，减少法务成本
- **电子签章**：支持简单的电子签名功能
- **微信通知**：订单状态变更自动通知相关人员

**验收标准**：
- 转化操作不超过2分钟
- 订单数据完整准确
- 支持订单快速查找

### 3.2 交付中台 (Delivery Cockpit) - 优先级：中

#### 3.2.1 订单执行管理 - 【核心模块】
**功能描述**：简化的订单执行跟踪，适合小微企业的生产特点

**具体需求**：
- **任务分解**：将订单分解为简单的执行任务
- **进度跟踪**：任务状态（未开始/进行中/已完成）
- **责任人**：每个任务指定责任人
- **简单BOM**：支持简单的物料清单管理

**小微企业适配优化**：
- **去复杂化**：取消复杂的BOM锁定机制，改为简单的变更记录
- **灵活调整**：支持订单执行过程中的灵活调整
- **移动操作**：生产人员可通过手机更新任务状态

**验收标准**：
- 任务创建时间不超过10分钟
- 状态更新实时同步
- 支持任务优先级设置

#### 3.2.2 成本归集与跟踪 - 【核心模块】
**功能描述**：简单易用的成本记录，让每笔花费都有归属

**具体需求**：
- **采购成本**：采购时选择关联订单，自动归集成本
- **人工成本**：简单的工时记录（开始/结束时间）
- **其他费用**：运费、外协费等杂项费用快速录入
- **成本汇总**：实时显示订单总成本和利润

**小微企业适配优化**：
- **简化操作**：取消复杂的成本分摊规则，采用直接归集
- **批量录入**：支持Excel批量导入成本数据
- **预警简化**：成本超预算30%时简单提醒

**验收标准**：
- 成本录入时间不超过1分钟
- 成本数据实时更新
- 支持成本明细导出

#### 3.2.3 订单看板 - 【核心模块】
**功能描述**：一目了然的订单全景视图

**具体需求**：
- **看板设计**：卡片式订单展示（客户、金额、状态、利润）
- **状态管理**：拖拽式状态更新（待生产→生产中→已发货→已完成）
- **利润监控**：实时显示预估利润vs实际利润
- **快速筛选**：按客户、状态、时间等维度快速筛选

**小微企业适配优化**：
- **界面简洁**：避免信息过载，突出关键指标
- **操作直观**：拖拽操作，降低学习成本
- **移动友好**：手机端也能方便查看和操作

**验收标准**：
- 看板加载时间不超过2秒
- 拖拽操作响应流畅
- 支持订单快速搜索

### 3.3 财报后台 (Profit Dashboard) - 优先级：高

#### 3.3.1 订单利润分析 - 【核心模块】
**功能描述**：让老板随时掌握每个订单的盈利情况

**具体需求**：
- **订单利润表**：收入、成本、毛利的清晰展示
- **对比分析**：预估利润vs实际利润的差异分析
- **趋势图表**：订单利润趋势的可视化展示
- **快速导出**：支持Excel导出，方便线下分析

**小微企业适配优化**：
- **界面简化**：避免复杂的财务术语，用通俗易懂的表达
- **关键指标**：突出毛利率、回款情况等核心指标
- **实时更新**：成本发生变化时，利润数据实时更新

**验收标准**：
- 利润计算准确率100%
- 报表生成时间不超过5秒
- 支持手机端查看

#### 3.3.2 绩效管理 - 【可选模块】
**功能描述**：简单的绩效考核，激励员工关注利润

**具体需求**：
- **绩效公式**：支持简单的绩效计算公式设置
- **利润提成**：业务员提成与订单毛利率挂钩
- **排行榜**：员工利润贡献排行榜
- **月度总结**：自动生成月度绩效报告

**小微企业适配优化**：
- **公式简单**：避免复杂的绩效计算，采用简单的阶梯提成
- **透明公开**：员工可查看自己的绩效数据
- **灵活调整**：老板可随时调整绩效规则

**验收标准**：
- 绩效计算无误差
- 数据更新及时
- 操作简单易懂

#### 3.3.3 经营分析 - 【核心模块】
**功能描述**：简单实用的经营数据分析

**具体需求**：
- **客户分析**：哪些客户最赚钱，哪些客户需要重点维护
- **产品分析**：哪类产品利润率最高，哪些产品需要调价
- **时间分析**：月度、季度利润趋势分析
- **业务员分析**：业务员的利润贡献排名

**小微企业适配优化**：
- **分析简化**：提供预设的分析模板，避免复杂的自定义分析
- **图表直观**：用简单的柱状图、饼图展示数据
- **结论明确**：系统自动给出分析结论和建议

**验收标准**：
- 分析报告生成时间不超过10秒
- 图表清晰易懂
- 支持一键分享给团队

## 4. AI助手功能需求 - 优先级：低（第二期实施）

### 4.1 智能报价助手 - 【增值功能】
**功能描述**：基于历史数据的智能报价建议

**具体需求**：
- **相似订单推荐**：根据产品特征推荐相似历史订单
- **价格建议**：基于历史成交价格给出报价建议
- **风险提示**：识别异常低价或高价报价

**小微企业适配**：
- **简单易懂**：避免复杂的AI算法解释，直接给出建议
- **可关闭**：企业可选择关闭AI功能，完全手动操作

### 4.2 成本预警助手 - 【增值功能】
**功能描述**：智能监控成本异常

**具体需求**：
- **超支预警**：成本超预算时自动提醒
- **异常识别**：识别异常高的成本项目
- **优化建议**：基于历史数据提供成本优化建议

### 4.3 经营洞察助手 - 【增值功能】
**功能描述**：智能经营分析和建议

**具体需求**：
- **趋势预测**：基于历史数据预测业务趋势
- **问题识别**：自动识别经营中的潜在问题
- **改进建议**：提供具体的改进建议

## 5. 非功能性需求

### 5.1 性能需求
- 系统响应时间：页面加载不超过3秒
- 并发用户数：支持100个并发用户
- 数据处理能力：支持10万条订单数据
- 可用性：99.5%以上

### 5.2 安全需求
- 用户身份认证
- 数据传输加密
- 操作日志记录
- 数据备份机制

### 5.3 兼容性需求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 支持移动端访问
- 支持主流操作系统

## 6. 用户角色与权限

### 6.1 用户角色定义
- **超级管理员**：系统全部权限
- **老板/高管**：查看所有订单利润数据，设置规则
- **业务经理**：管理本部门业务员和订单
- **业务员**：创建报价、管理客户、查看负责订单
- **生产经理**：管理生产计划、成本归集
- **生产员工**：记录工时、查看任务
- **财务人员**：查看财务数据、生成报表
- **采购员**：创建采购单、管理供应商

### 6.2 权限矩阵
[详细的权限矩阵表格，定义每个角色对每个功能模块的权限]

## 7. 数据需求

### 7.1 核心数据实体
- 客户信息
- 订单信息
- BOM数据
- 物料信息
- 供应商信息
- 员工信息
- 成本数据
- 财务数据

### 7.2 数据质量要求
- 数据完整性：关键字段不能为空
- 数据一致性：关联数据保持一致
- 数据准确性：定期校验数据准确性
- 数据时效性：实时数据更新

## 8. 集成需求

### 8.1 外部系统集成
- ERP系统集成
- 财务系统集成
- 物料价格系统集成
- 邮件系统集成

### 8.2 API需求
- RESTful API设计
- API文档完整
- API安全认证
- API性能监控

## 9. 实施计划

### 9.1 项目阶段
- **第一阶段**：业务前台开发（3个月）
- **第二阶段**：交付中台开发（3个月）
- **第三阶段**：财报后台开发（2个月）
- **第四阶段**：AI助手集成（2个月）
- **第五阶段**：系统测试和上线（1个月）

### 9.2 里程碑
- 需求确认完成
- 系统设计完成
- 各模块开发完成
- 集成测试完成
- 用户验收完成
- 系统正式上线

## 10. 验收标准

### 10.1 功能验收
- 所有功能需求100%实现
- 用户操作流程顺畅
- 数据计算准确无误

### 10.2 性能验收
- 满足所有性能指标要求
- 压力测试通过
- 稳定性测试通过

### 10.3 用户验收
- 用户培训完成
- 用户操作熟练度达标
- 用户满意度达到90%以上

---

**文档版本**：V1.0  
**创建日期**：2025-08-26  
**最后更新**：2025-08-26  
**文档状态**：草稿
