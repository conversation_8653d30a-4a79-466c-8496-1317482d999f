# 小微企业经营管理系统需求文档

## 1. 项目概述

### 1.1 项目背景
传统企业管理模式下，各部门存在天然的"利益墙"，导致业务部门追求合同额、生产部门追求交付效率、财务部门事后核算，缺乏统一的利润导向。本系统旨在打破部门壁垒，建立以订单利润为核心的统一管理体系。

### 1.2 核心理念
**告别部门本位，回归客户价值**。每一份订单都是一个独立的经营单元(Profit Center)，从业务到财务，只对订单的最终利润负责。

### 1.3 项目目标
- 建立以订单为中心的数据管理体系
- 实现订单利润的实时监控和预警
- 打通业务、生产、财务的数据壁垒
- 建立基于利润的绩效考核机制
- 通过AI助手提升决策智能化水平

## 2. 系统架构

### 2.1 总体架构
**一个中心，三大支柱**
- **一个中心**：客户订单（所有数据、流程、权限都与订单ID强关联）
- **三大支柱**：业务前台、交付中台、财报后台

### 2.2 技术架构要求
- 微服务架构，支持模块化部署
- 实时数据处理能力
- 移动端友好的响应式设计
- 支持AI模型集成
- 数据安全和权限管控

## 3. 功能需求

### 3.1 业务前台 (Revenue Engine)

#### 3.1.1 客户与商机管理 (CRM-Lite)
**功能描述**：统一管理客户信息和销售机会

**具体需求**：
- 客户基础信息管理（公司名称、联系人、联系方式、地址等）
- 客户分级管理（A/B/C级客户）
- 销售机会跟踪（商机来源、阶段、预计成交时间、金额）
- 客户沟通记录管理
- 客户标签和分类管理

**验收标准**：
- 支持客户信息的增删改查
- 支持客户信息导入导出
- 提供客户360度视图
- 支持销售漏斗分析

#### 3.1.2 智能报价与利润预估器
**功能描述**：在签单前清晰预估订单利润空间

**具体需求**：
- 标准BOM模板库管理
- 历史订单BOM快速调用
- 实时物料参考价获取
- 预估成本自动计算（材料成本、人工成本、运费等）
- 实时毛利率计算
- 报价单生成和版本管理

**验收标准**：
- 报价准确率达到85%以上
- 报价生成时间不超过30分钟
- 支持多版本报价对比
- 自动保存报价历史记录

#### 3.1.3 规则引擎与风险控制
**功能描述**：设定利润红线，自动预警和审批

**具体需求**：
- 最低毛利率红线设置（可按产品线、客户等维度设置）
- 自动预警机制（低于红线时触发）
- 审批流程配置（超级管理员、部门经理等多级审批）
- 风险评估报告生成

**验收标准**：
- 预警响应时间不超过1分钟
- 审批流程可灵活配置
- 风险评估准确率达到90%以上

#### 3.1.4 合同与订单转化
**功能描述**：报价转化为正式订单，数据无缝流转

**具体需求**：
- 一键报价转订单功能
- 合同条款管理
- 订单状态跟踪
- 数据完整性校验

**验收标准**：
- 转化过程数据零丢失
- 转化时间不超过5分钟
- 支持批量操作

### 3.2 交付中台 (Delivery Cockpit)

#### 3.2.1 订单BOM确认与锁定
**功能描述**：将订单转化为可执行的交付任务

**具体需求**：
- BOM确认流程管理
- BOM锁定机制（锁定后修改需审批）
- BOM变更历史追踪
- 技术评审流程

**验收标准**：
- BOM确认时间不超过2个工作日
- 变更记录100%可追溯
- 支持BOM对比功能

#### 3.2.2 订单成本实时归集
**功能描述**：实时收集和归集订单相关成本

**具体需求**：
- 采购成本归集（采购单必须关联订单ID）
- 工时成本归集（扫码/点击记录工时）
- 其他费用归集（外协、运费等）
- 成本分摊规则设置
- 实时成本计算

**验收标准**：
- 成本归集实时性不超过1小时
- 成本归集准确率达到95%以上
- 支持成本明细查询

#### 3.2.3 订单进度可视化
**功能描述**：实时展示订单执行进度和成本状况

**具体需求**：
- 订单看板设计（状态、进度、成本等）
- 实时成本/预算成本进度条
- 超支预警（红灯提醒）
- 关键节点里程碑管理
- 移动端查看支持

**验收标准**：
- 数据刷新频率不超过5分钟
- 支持多维度筛选和排序
- 移动端响应时间不超过3秒

### 3.3 财报后台 (Profit Dashboard)

#### 3.3.1 订单P&L驾驶舱
**功能描述**：为每个订单生成实时利润表

**具体需求**：
- 单订单P&L报表生成
- 实时收入、成本、毛利计算
- 预估vs实际对比分析
- 权限控制（相关人员可见）
- 数据导出功能

**验收标准**：
- 报表生成时间不超过10秒
- 数据准确率达到99%以上
- 支持多种导出格式

#### 3.3.2 绩效与利润挂钩
**功能描述**：建立基于利润的绩效考核体系

**具体需求**：
- 绩效计算公式配置
- 奖金与毛利率关联机制
- 员工绩效看板
- 绩效历史记录管理
- 绩效报告生成

**验收标准**：
- 绩效计算准确率100%
- 支持灵活的计算公式配置
- 绩效数据可追溯

#### 3.3.3 多维度利润分析
**功能描述**：提供多角度的利润分析报告

**具体需求**：
- 客户利润贡献分析
- 产品线利润分析
- 业务员利润贡献分析
- 时间维度利润趋势分析
- 自定义分析维度

**验收标准**：
- 支持至少10个分析维度
- 报告生成时间不超过30秒
- 支持图表可视化展示

## 4. AI助手功能需求

### 4.1 智能报价助手
**功能描述**：基于历史数据提供报价建议

**具体需求**：
- 历史订单数据分析
- 相似订单识别
- 成本优化建议
- 风险提示

### 4.2 成本监控助手
**功能描述**：实时监控成本异常并预警

**具体需求**：
- 成本超支预警
- 异常成本识别
- 成本优化建议
- 自动报告生成

### 4.3 利润复盘助手
**功能描述**：订单完成后自动生成复盘报告

**具体需求**：
- 预估vs实际差异分析
- 问题根因分析
- 改进建议提供
- 经验总结

## 5. 非功能性需求

### 5.1 性能需求
- 系统响应时间：页面加载不超过3秒
- 并发用户数：支持100个并发用户
- 数据处理能力：支持10万条订单数据
- 可用性：99.5%以上

### 5.2 安全需求
- 用户身份认证
- 数据传输加密
- 操作日志记录
- 数据备份机制

### 5.3 兼容性需求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 支持移动端访问
- 支持主流操作系统

## 6. 用户角色与权限

### 6.1 用户角色定义
- **超级管理员**：系统全部权限
- **老板/高管**：查看所有订单利润数据，设置规则
- **业务经理**：管理本部门业务员和订单
- **业务员**：创建报价、管理客户、查看负责订单
- **生产经理**：管理生产计划、成本归集
- **生产员工**：记录工时、查看任务
- **财务人员**：查看财务数据、生成报表
- **采购员**：创建采购单、管理供应商

### 6.2 权限矩阵
[详细的权限矩阵表格，定义每个角色对每个功能模块的权限]

## 7. 数据需求

### 7.1 核心数据实体
- 客户信息
- 订单信息
- BOM数据
- 物料信息
- 供应商信息
- 员工信息
- 成本数据
- 财务数据

### 7.2 数据质量要求
- 数据完整性：关键字段不能为空
- 数据一致性：关联数据保持一致
- 数据准确性：定期校验数据准确性
- 数据时效性：实时数据更新

## 8. 集成需求

### 8.1 外部系统集成
- ERP系统集成
- 财务系统集成
- 物料价格系统集成
- 邮件系统集成

### 8.2 API需求
- RESTful API设计
- API文档完整
- API安全认证
- API性能监控

## 9. 实施计划

### 9.1 项目阶段
- **第一阶段**：业务前台开发（3个月）
- **第二阶段**：交付中台开发（3个月）
- **第三阶段**：财报后台开发（2个月）
- **第四阶段**：AI助手集成（2个月）
- **第五阶段**：系统测试和上线（1个月）

### 9.2 里程碑
- 需求确认完成
- 系统设计完成
- 各模块开发完成
- 集成测试完成
- 用户验收完成
- 系统正式上线

## 10. 验收标准

### 10.1 功能验收
- 所有功能需求100%实现
- 用户操作流程顺畅
- 数据计算准确无误

### 10.2 性能验收
- 满足所有性能指标要求
- 压力测试通过
- 稳定性测试通过

### 10.3 用户验收
- 用户培训完成
- 用户操作熟练度达标
- 用户满意度达到90%以上

---

**文档版本**：V1.0  
**创建日期**：2025-08-26  
**最后更新**：2025-08-26  
**文档状态**：草稿
