declare const errors: {
    readonly 0: "Illegal state";
    readonly 1: "Immer drafts cannot have computed properties";
    readonly 2: "This object has been frozen and should not be mutated";
    readonly 3: (data: any) => string;
    readonly 4: "An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.";
    readonly 5: "Immer forbids circular references";
    readonly 6: "The first or second argument to `produce` must be a function";
    readonly 7: "The third argument to `produce` must be a function or undefined";
    readonly 8: "First argument to `createDraft` must be a plain object, an array, or an immerable object";
    readonly 9: "First argument to `finishDraft` must be a draft returned by `createDraft`";
    readonly 10: "The given draft is already finalized";
    readonly 11: "Object.defineProperty() cannot be used on an Immer draft";
    readonly 12: "Object.setPrototypeOf() cannot be used on an Immer draft";
    readonly 13: "Immer only supports deleting array indices";
    readonly 14: "Immer only supports setting array indices and the 'length' property";
    readonly 15: (path: string) => string;
    readonly 16: "Sets cannot have \"replace\" patches.";
    readonly 17: (op: string) => string;
    readonly 18: (plugin: string) => string;
    readonly 20: "Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available";
    readonly 21: (thing: string) => string;
    readonly 22: (thing: string) => string;
    readonly 23: (thing: string) => string;
    readonly 24: "Patching reserved attributes like __proto__, prototype and constructor is not allowed";
};
export declare function die(error: keyof typeof errors, ...args: any[]): never;
export {};
//# sourceMappingURL=errors.d.ts.map