import CalendarLocale from "rc-picker/es/locale/vi_VN";
import TimePickerLocale from '../../time-picker/locale/vi_VN';
// Merge into a locale object
const locale = {
  lang: Object.assign({
    placeholder: '<PERSON>ọ<PERSON> thời điểm',
    yearPlaceholder: '<PERSON>ọ<PERSON> năm',
    quarterPlaceholder: '<PERSON>ọn quý',
    monthPlaceholder: '<PERSON>ọ<PERSON> tháng',
    weekPlaceholder: 'Chọn tuần',
    rangePlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeYearPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeQuarterPlaceholder: ['<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc'],
    rangeMonthPlaceholder: ['Tháng bắt đầu', 'Tháng kết thúc'],
    rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc'],
    shortMonths: ['Th 01', 'Th 02', 'Th 03', 'Th 04', 'Th 05', 'Th 06', 'Th 07', 'Th 08', 'Th 09', 'Th 10', 'Th 11', 'Th 12'],
    shortWeekDays: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']
  }, CalendarLocale),
  timePickerLocale: Object.assign({}, TimePickerLocale)
};
// All settings at:
// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json
export default locale;