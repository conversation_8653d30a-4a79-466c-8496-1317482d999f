{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type EChartsOption = any;\n\nexport type EChartsInstance = any;\n\nexport type Opts = {\n  readonly devicePixelRatio?: number;\n  readonly renderer?: 'canvas' | 'svg';\n  readonly width?: number | null | undefined | 'auto';\n  readonly height?: number | null | undefined | 'auto';\n  readonly locale?: string;\n};\n\nexport type EChartsReactProps = {\n  /**\n   * echarts library entry, use it for import necessary.\n   */\n  readonly echarts?: any;\n  /**\n   * `className` for container\n   */\n  readonly className?: string;\n  /**\n   * `style` for container\n   */\n  readonly style?: CSSProperties;\n  /**\n   * echarts option\n   */\n  readonly option: EChartsOption;\n  /**\n   * echarts theme config, can be:\n   * 1. theme name string\n   * 2. theme object\n   */\n  readonly theme?: string | Record<string, any>;\n  /**\n   * notMerge config for echarts, default is `false`\n   */\n  readonly notMerge?: boolean;\n  /**\n   * lazyUpdate config for echarts, default is `false`\n   */\n  readonly lazyUpdate?: boolean;\n  /**\n   * showLoading config for echarts, default is `false`\n   */\n  readonly showLoading?: boolean;\n  /**\n   * loadingOption config for echarts, default is `null`\n   */\n  readonly loadingOption?: any;\n  /**\n   * echarts opts config, default is `{}`\n   */\n  readonly opts?: Opts;\n  /**\n   * when after chart reander, do the callback widht echarts instance\n   */\n  readonly onChartReady?: (instance: EChartsInstance) => void;\n  /**\n   * bind events, default is `{}`\n   */\n  readonly onEvents?: Record<string, Function>;\n  /**\n   * should update echarts options\n   */\n  readonly shouldSetOption?: (prevProps: EChartsReactProps, props: EChartsReactProps) => boolean;\n};\n"]}