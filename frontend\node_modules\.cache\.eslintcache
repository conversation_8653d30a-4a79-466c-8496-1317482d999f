[{"D:\\customerDemo\\Link-BOM\\frontend\\src\\index.js": "1", "D:\\customerDemo\\Link-BOM\\frontend\\src\\App.js": "2", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\index.js": "3", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\authSlice.js": "4", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Layout\\Layout.js": "5", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Auth\\Login.js": "6", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerList.js": "7", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationDetail.js": "8", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerDetail.js": "9", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationList.js": "10", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "11", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderDetail.js": "12", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableList.js": "13", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderList.js": "14", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\BusinessAnalysis.js": "15", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\ProfitAnalysis.js": "16", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\PerformanceManagement.js": "17", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\FinancialReports.js": "18", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableDetail.js": "19", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Help\\HelpCenter.js": "20", "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Settings\\Settings.js": "21", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\customerSlice.js": "22", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\orderSlice.js": "23", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\dashboardSlice.js": "24", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\quotationSlice.js": "25", "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\receivableSlice.js": "26", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\OrderStatusChart.js": "27", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\ReceivableAgingChart.js": "28", "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\RevenueChart.js": "29"}, {"size": 900, "mtime": 1756213080951, "results": "30", "hashOfConfig": "31"}, {"size": 5185, "mtime": 1756213332523, "results": "32", "hashOfConfig": "31"}, {"size": 758, "mtime": 1756213106018, "results": "33", "hashOfConfig": "31"}, {"size": 4953, "mtime": 1756213128740, "results": "34", "hashOfConfig": "31"}, {"size": 9173, "mtime": 1756213399088, "results": "35", "hashOfConfig": "31"}, {"size": 6219, "mtime": 1756213361676, "results": "36", "hashOfConfig": "31"}, {"size": 15467, "mtime": 1756213537269, "results": "37", "hashOfConfig": "31"}, {"size": 1017, "mtime": 1756213640313, "results": "38", "hashOfConfig": "31"}, {"size": 12757, "mtime": 1756213584566, "results": "39", "hashOfConfig": "31"}, {"size": 10884, "mtime": 1756213628014, "results": "40", "hashOfConfig": "31"}, {"size": 9959, "mtime": 1756213435379, "results": "41", "hashOfConfig": "31"}, {"size": 1023, "mtime": 1756213690951, "results": "42", "hashOfConfig": "31"}, {"size": 7836, "mtime": 1756213723510, "results": "43", "hashOfConfig": "31"}, {"size": 9832, "mtime": 1756213678067, "results": "44", "hashOfConfig": "31"}, {"size": 571, "mtime": 1756213762061, "results": "45", "hashOfConfig": "31"}, {"size": 552, "mtime": 1756213744630, "results": "46", "hashOfConfig": "31"}, {"size": 569, "mtime": 1756213753464, "results": "47", "hashOfConfig": "31"}, {"size": 559, "mtime": 1756213770328, "results": "48", "hashOfConfig": "31"}, {"size": 1032, "mtime": 1756213735889, "results": "49", "hashOfConfig": "31"}, {"size": 553, "mtime": 1756213778223, "results": "50", "hashOfConfig": "31"}, {"size": 531, "mtime": 1756213786736, "results": "51", "hashOfConfig": "31"}, {"size": 6638, "mtime": 1756213162005, "results": "52", "hashOfConfig": "31"}, {"size": 8659, "mtime": 1756213198223, "results": "53", "hashOfConfig": "31"}, {"size": 5277, "mtime": 1756213302172, "results": "54", "hashOfConfig": "31"}, {"size": 7980, "mtime": 1756213232275, "results": "55", "hashOfConfig": "31"}, {"size": 9267, "mtime": 1756213272125, "results": "56", "hashOfConfig": "31"}, {"size": 1467, "mtime": 1756213460534, "results": "57", "hashOfConfig": "31"}, {"size": 2024, "mtime": 1756213472964, "results": "58", "hashOfConfig": "31"}, {"size": 2128, "mtime": 1756213449761, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11yy90s", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\customerDemo\\Link-BOM\\frontend\\src\\index.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\App.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\index.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\authSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Layout\\Layout.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Auth\\Login.js", ["147"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerList.js", ["148", "149"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Customer\\CustomerDetail.js", ["150"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Quotation\\QuotationList.js", ["151", "152", "153"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Dashboard\\Dashboard.js", ["154", "155", "156", "157", "158"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableList.js", ["159", "160"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Order\\OrderList.js", ["161", "162", "163"], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\BusinessAnalysis.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\ProfitAnalysis.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\PerformanceManagement.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Analysis\\FinancialReports.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Receivable\\ReceivableDetail.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Help\\HelpCenter.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\customerSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\dashboardSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\quotationSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\store\\slices\\receivableSlice.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\OrderStatusChart.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\ReceivableAgingChart.js", [], [], "D:\\customerDemo\\Link-BOM\\frontend\\src\\components\\Charts\\RevenueChart.js", [], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 1, "column": 17, "nodeType": "166", "messageId": "167", "endLine": 1, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "168", "line": 24, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 24, "endColumn": 17}, {"ruleId": "164", "severity": 1, "message": "169", "line": 151, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 151, "endColumn": 23}, {"ruleId": "164", "severity": 1, "message": "170", "line": 17, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 17, "endColumn": 9}, {"ruleId": "164", "severity": 1, "message": "165", "line": 1, "column": 17, "nodeType": "166", "messageId": "167", "endLine": 1, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "171", "line": 19, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 11}, {"ruleId": "164", "severity": 1, "message": "168", "line": 23, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 23, "endColumn": 17}, {"ruleId": "164", "severity": 1, "message": "172", "line": 15, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 15, "endColumn": 6}, {"ruleId": "164", "severity": 1, "message": "173", "line": 16, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 16, "endColumn": 10}, {"ruleId": "164", "severity": 1, "message": "174", "line": 20, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 20, "endColumn": 20}, {"ruleId": "164", "severity": 1, "message": "175", "line": 23, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 23, "endColumn": 15}, {"ruleId": "164", "severity": 1, "message": "176", "line": 94, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 94, "endColumn": 22}, {"ruleId": "164", "severity": 1, "message": "177", "line": 2, "column": 23, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 34}, {"ruleId": "164", "severity": 1, "message": "168", "line": 19, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 17}, {"ruleId": "164", "severity": 1, "message": "165", "line": 1, "column": 17, "nodeType": "166", "messageId": "167", "endLine": 1, "endColumn": 25}, {"ruleId": "164", "severity": 1, "message": "178", "line": 18, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 18, "endColumn": 8}, {"ruleId": "164", "severity": 1, "message": "168", "line": 22, "column": 3, "nodeType": "166", "messageId": "167", "endLine": 22, "endColumn": 17}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'SearchOutlined' is defined but never used.", "'getStatusColor' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Progress' is defined but never used.", "'Tag' is defined but never used.", "'Divider' is defined but never used.", "'ArrowDownOutlined' is defined but never used.", "'TeamOutlined' is defined but never used.", "'getAlertColor' is assigned a value but never used.", "'useDispatch' is defined but never used.", "'Badge' is defined but never used."]